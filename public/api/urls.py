from django.urls import path, include
from public.api.views import RegisterRestaurantView, FeaturedRestaurantsView
from tenants.views import tenant_home


urlpatterns = [
    path('', tenant_home, name='tenant-home'),
    path('register-restaurant/', RegisterRestaurantView.as_view(), name='restaurant-register'),
    path('featured-restaurants/', FeaturedRestaurantsView.as_view(), name='featured-restaurants'),
    path('customer/', include('users.api.public')),
]
