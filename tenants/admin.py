from django.contrib import admin
from django.core.mail import send_mail
from django.contrib.auth.hashers import make_password
from django_tenants.utils import schema_context
from .models import Client, PendingClient, Domain
from django.contrib.auth import get_user_model  # ? Correct import
import secrets

User = get_user_model()  # Get custom user model

@admin.action(description="Approve selected restaurants")
def approve_restaurants(modeladmin, request, queryset):
    for pending in queryset:
        if pending.approved:
            continue
        
        password = secrets.token_urlsafe(12)
        
        # Create tenant
        from django.utils.timezone import now
        from datetime import timedelta
        from core.settings import DEFAULT_TRIAL_PERIOD

        # Set trial end date
        trial_end_date = now().date() + timedelta(days=DEFAULT_TRIAL_PERIOD)

        client = Client.objects.create(
            restaurant_name=pending.restaurant_name,
            schema_name=pending.schema_name,
            manager_email=pending.manager_email,
            manager_firstname=pending.manager_firstname,
            manager_lastname=pending.manager_lastname,
            manager_phone=pending.manager_phone,
            phone_number=pending.restaurant_phone,  # Use restaurant_phone instead of manager_phone
            address=pending.address,
            latitude=pending.latitude,
            longitude=pending.longitude,
            description=pending.description,
            logo=pending.logo,
            banner=pending.banner,
            active=True,
            paid_until=trial_end_date,
            on_trial=True,
            tier='basic'
        )
        domain_name = f"{client.schema_name.replace('_','-')}.localhost"
        domain = Domain.objects.create(
            domain=domain_name,
            tenant=client,
            is_primary=True,
            is_active=True
        )
        
        username = f"manager@{domain_name}"
        with schema_context(client.schema_name):
            user = User.objects.create_user(
                username=username,
                email=pending.manager_email,
                password=password,
                first_name=pending.manager_firstname,
                last_name=pending.manager_lastname,
                is_active=True,
                role='manager',  # Ensure role is lowercase to match choices
                restaurant=client  # Link user to tenant
            )
            user.is_staff = True  # Required for admin access
            user.save()
  
        send_mail(
            'Your Restaurant Has Been Approved',
            f'Login URL: http://{domain_name}:8000\nUsername: {username}\nPassword: {password}',
            '<EMAIL>',
            [pending.manager_email],
            fail_silently=False,
        )
        
        pending.approved = True
        pending.save()

@admin.register(PendingClient)
class PendingClientAdmin(admin.ModelAdmin):
    list_display = ('restaurant_name', 'schema_name', 'manager_email', 'approved', 'created_on')
    list_filter = ('approved',)
    actions = [approve_restaurants]

    def get_queryset(self, request):
        # Show only unapproved entries
        return super().get_queryset(request).filter(approved=False)

@admin.register(Client)
class ClientAdmin(admin.ModelAdmin):
    list_display = ('restaurant_name', 'tier', 'active', 'on_trial', 'paid_until')
    list_filter = ('tier', 'active', 'on_trial')
    search_fields = ('restaurant_name', 'schema_restaurant_name')
    actions = ['activate_tenants', 'deactivate_tenants']

    def get_queryset(self, request):
        return super().get_queryset(request).filter(active=True)
    
    def activate_tenants(self, request, queryset):
        queryset.update(active=True)
    activate_tenants.short_description = "Activate selected tenants"

    def deactivate_tenants(self, request, queryset):
        queryset.update(active=False)
    deactivate_tenants.short_description = "Deactivate selected tenants"

@admin.register(Domain)
class DomainAdmin(admin.ModelAdmin):
    list_display = ('domain', 'tenant', 'is_primary')
    list_filter = ('is_primary',)
    search_fields = ('domain', 'tenant__restaurant_name')
