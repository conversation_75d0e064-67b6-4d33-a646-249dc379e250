#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to manually fix the Tahit restaurant approval issue
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from tenants.models import Client, PendingClient, Domain
from django.contrib.auth import get_user_model
from django.utils.timezone import now
from datetime import timedelta
from core.settings import DEFAULT_TRIAL_PERIOD
import secrets

User = get_user_model()

def fix_tahit_approval():
    try:
        # Get the Tahit restaurant
        pending = PendingClient.objects.get(schema_name='kips')
        print(f"Found pending restaurant: {pending.restaurant_name}")
        
        # Check if it's already approved but not in Client table
        if pending.approved:
            print("Restaurant is marked as approved but not in Client table")
            
            # Check if Client already exists
            if Client.objects.filter(schema_name='kips').exists():
                print("Client already exists!")
                return
            
            # Reset approval status and try again
            pending.approved = False
            pending.save()
            print("Reset approval status")
        
        # Now run the fixed approval process
        password = secrets.token_urlsafe(12)
        
        # Set trial end date
        trial_end_date = now().date() + timedelta(days=DEFAULT_TRIAL_PERIOD)
        
        print(f"Creating client with trial end date: {trial_end_date}")
        
        # Create tenant with proper paid_until date
        client = Client.objects.create(
            restaurant_name=pending.restaurant_name,
            schema_name=pending.schema_name,
            manager_email=pending.manager_email,
            manager_firstname=pending.manager_firstname,
            manager_lastname=pending.manager_lastname,
            manager_phone=pending.manager_phone,
            phone_number=pending.restaurant_phone,
            address=pending.address,
            latitude=pending.latitude,
            longitude=pending.longitude,
            description=pending.description,
            logo=pending.logo,
            banner=pending.banner,
            active=True,
            paid_until=trial_end_date,
            on_trial=True,
            tier='basic'
        )
        
        print(f"Created client: {client.restaurant_name} with schema: {client.schema_name}")
        
        # Create domain
        domain_name = f"{client.schema_name.replace('_','-')}.localhost"
        domain = Domain.objects.create(
            domain=domain_name,
            tenant=client,
            is_primary=True,
            is_active=True
        )
        
        print(f"Created domain: {domain_name}")
        
        # Create user in tenant schema
        from django_tenants.utils import schema_context
        username = f"manager@{domain_name}"
        
        with schema_context(client.schema_name):
            user = User.objects.create_user(
                username=username,
                email=pending.manager_email,
                password=password,
                first_name=pending.manager_firstname,
                last_name=pending.manager_lastname,
                is_active=True,
                role='manager',
                restaurant=client
            )
            user.is_staff = True
            user.save()
            
        print(f"Created user: {username}")
        print(f"Password: {password}")
        
        # Mark as approved
        pending.approved = True
        pending.save()
        
        print("✅ Successfully approved Kips restaurant!")
        print(f"Login URL: http://{domain_name}:8000")
        print(f"Username: {username}")
        print(f"Password: {password}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_tahit_approval()
